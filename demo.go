// Demo program showing the Caesar cipher decoder with different messages
package main

import (
	"fmt"
	"strings"
	"unicode"
)

// Same functions as main.go but in a demo context
func decodeCaesar(text string, shift int) string {
	var result strings.Builder
	
	for _, char := range text {
		if unicode.IsLetter(char) {
			var base rune
			if unicode.IsUpper(char) {
				base = 'A'
			} else {
				base = 'a'
			}
			
			shifted := (char - base - rune(shift) + 26) % 26
			result.WriteRune(base + shifted)
		} else {
			result.WriteRune(char)
		}
	}
	
	return result.String()
}

func scoreText(text string) int {
	score := 0
	lowerText := strings.ToLower(text)
	
	commonWords := map[string]int{
		"the": 15, "and": 12, "that": 10, "have": 8,
		"for": 8, "not": 8, "with": 8, "you": 8,
		"this": 7, "but": 7, "his": 7, "from": 7,
		"they": 6, "she": 6, "her": 6, "been": 6,
		"than": 6, "its": 6, "now": 6, "more": 6,
		"will": 5, "are": 5, "was": 5, "one": 5,
		"all": 5, "would": 5, "there": 5, "their": 5,
	}
	
	words := strings.Fields(lowerText)
	for _, word := range words {
		cleanWord := strings.Trim(word, ".,!?;:")
		if wordScore, exists := commonWords[cleanWord]; exists {
			score += wordScore
		}
	}
	
	for _, char := range lowerText {
		switch char {
		case 'e':
			score += 2
		case 't', 'a', 'o', 'i', 'n':
			score += 1
		}
	}
	
	return score
}

func findBestDecryption(ciphertext string) (string, int, int) {
	bestScore := 0
	bestShift := 0
	bestDecoded := ""
	
	for shift := 1; shift <= 25; shift++ {
		decoded := decodeCaesar(ciphertext, shift)
		score := scoreText(decoded)
		
		if score > bestScore {
			bestScore = score
			bestShift = shift
			bestDecoded = decoded
		}
	}
	
	return bestDecoded, bestShift, bestScore
}

func main() {
	fmt.Println("Caesar Cipher Decoder - Demo")
	fmt.Println("============================")
	fmt.Println()
	
	// Test cases
	testCases := []struct {
		name        string
		ciphertext  string
		description string
	}{
		{
			name:        "Original Challenge",
			ciphertext:  "Nzyrclefwletzyd zy nclnvtyr esp Zneznz LT nslwwpyrp!",
			description: "The Octoco AI challenge message",
		},
		{
			name:        "Simple Test",
			ciphertext:  "Wkh txlfn eurzq ira mxpsv ryhu wkh odcb grj",
			description: "Classic pangram with shift 3",
		},
		{
			name:        "Another Test",
			ciphertext:  "Vjku ku c vguv qh vjg Ecguct ekrjgt",
			description: "Simple test message",
		},
	}
	
	for i, test := range testCases {
		fmt.Printf("%d. %s\n", i+1, test.name)
		fmt.Printf("   Description: %s\n", test.description)
		fmt.Printf("   Encrypted: %s\n", test.ciphertext)
		
		decoded, shift, score := findBestDecryption(test.ciphertext)
		fmt.Printf("   Decoded (shift %d, score %d): %s\n", shift, score, decoded)
		fmt.Println()
	}
}
