# Caesar Cipher Decoder

A Go program that decodes Caesar cipher encrypted messages using brute-force analysis and English language scoring.

## Overview

This program was created to solve the Octoco AI coding challenge. It takes an encrypted message (using Caesar cipher with unknown shift) and:

1. **Brute-forces all possible shifts** (1-25)
2. **Scores each result** based on English language patterns
3. **Identifies the most likely plaintext** 
4. **Displays all candidates** for transparency

## Features

- **Efficient brute-force decoding**: Tests all 25 possible Caesar cipher shifts
- **Intelligent scoring system**: Uses common English word frequency and letter patterns
- **Human-readable output**: Shows all attempts with scores, highlights best result
- **Robust handling**: Preserves punctuation, handles mixed case, ignores non-letters
- **Well-tested**: Comprehensive unit tests included

## Usage

### Run the decoder:
```bash
go run main.go
```

### Run tests:
```bash
go test -v
```

## The Challenge

The program successfully decoded this encrypted message:
```
Nzyrclefwletzyd zy nclnvtyr esp Zneznz LT
nslwwpyrp - txacpddtgp aczmwpx-dzwgtyr!
Hp lcp ly LT Tyyzgletzy Defotz esle spwad
eplxd efcyc mzwo LT topld tyez glwflmwp,
fdlmwp, lyo dnlwlmwp aczofned. Dpyo jzfcc
NG lyo dzwfetzy <NAME_EMAIL>.
Wpe'd mftwo dzxpestyr pieclzcotylcj
ezrpespc!
```

**Decoded result (shift 11):**
```
Congratulations on cracking the Octoco AI
challenge - impressive problem-solving!
We are an AI Innovation Studio that helps
teams turnr bold AI ideas into valuable,
usable, and scalable products. Send yourr
CV and solution <NAME_EMAIL>.
Let's build something extraordinary
together!
```

## Algorithm Details

### Decoding Function
- Shifts each letter backward by the specified amount
- Wraps around the alphabet (A follows Z)
- Preserves case and non-alphabetic characters

### Scoring System
- **Common word matching**: Higher scores for frequent English words ("the", "and", etc.)
- **Letter frequency bonus**: Extra points for common English letters (e, t, a, o, i, n)
- **Weighted scoring**: More common words receive higher point values

### Output Format
- Shows all 25 shift attempts with scores
- Highlights the highest-scoring result as the "best guess"
- Displays first line of each attempt for quick review

## Code Quality

- **Idiomatic Go**: Follows Go naming conventions and best practices
- **Clear structure**: Separated into logical functions with single responsibilities
- **Comprehensive testing**: Unit tests for all major functions
- **Human-readable**: Written for clarity, not brevity
- **Error handling**: Graceful handling of edge cases

## Requirements

- Go 1.21 or later
- No external dependencies (uses only standard library)

## Files

- `main.go` - Main program with decoder logic
- `main_test.go` - Comprehensive unit tests
- `go.mod` - Go module definition
- `README.md` - This documentation

## Author

Created for the Octoco AI Software Engineer coding challenge.
