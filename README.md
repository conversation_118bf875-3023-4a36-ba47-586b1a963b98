# Caesar Cipher Decoder

A Go program that decodes Caesar cipher encrypted messages using brute-force analysis and English language scoring.

## Overview

This program was created to solve the Octoco AI coding challenge. It takes an encrypted message (using Caesar cipher with unknown shift) and:

1. **Brute-forces all possible shifts** (1-25)
2. **Scores each result** based on English language patterns
3. **Identifies the most likely plaintext** 
4. **Displays all candidates** for transparency

## Features

- **Efficient brute-force decoding**: Tests all 25 possible Caesar cipher shifts
- **Intelligent scoring system**: Uses common English word frequency and letter patterns
- **Human-readable output**: Shows all attempts with scores, highlights best result
- **Robust handling**: Preserves punctuation, handles mixed case, ignores non-letters
- **Well-tested**: Comprehensive unit tests included

## Usage

### Using Make (recommended):
```bash
make run          # Run the decoder (interactive input)
make demo         # Run with the challenge text automatically
make test         # Run all tests
make build        # Build executable to bin/caesercypher
make help         # Show all available commands
```

### Direct Go commands:
```bash
go run cmd/caesercypher/caesercypher.go  # Run the decoder
go test ./...                            # Run all tests
go build -o bin/caesercypher cmd/caesercypher/caesercypher.go  # Build
```

### Interactive Usage:
The program now accepts input from the terminal. Simply run it and paste your encrypted text, then press Enter twice to process.

## The Challenge

The program successfully decoded this encrypted message:
```
Nzyrclefwletzyd zy nclnvtyr esp Zneznz LT
nslwwpyrp - txacpddtgp aczmwpx-dzwgtyr!
Hp lcp ly LT Tyyzgletzy Defotz esle spwad
eplxd efcyc mzwo LT topld tyez glwflmwp,
fdlmwp, lyo dnlwlmwp aczofned. Dpyo jzfcc
NG lyo dzwfetzy <NAME_EMAIL>.
Wpe'd mftwo dzxpestyr pieclzcotylcj
ezrpespc!
```

**Decoded result (shift 11):**
```
Congratulations on cracking the Octoco AI
challenge - impressive problem-solving!
We are an AI Innovation Studio that helps
teams turnr bold AI ideas into valuable,
usable, and scalable products. Send yourr
CV and solution <NAME_EMAIL>.
Let's build something extraordinary
together!
```

## How It Works

### Simple Approach
- Tries all 25 possible shifts (1-25)
- Scores each result based on how "English-like" it looks
- Picks the highest scoring result

### Scoring
- Common English words get points ("the"=15, "and"=12, etc.)
- Letter 'e' gets bonus points (appears most in English)
- Other common letters (t,a,o,i,n) get smaller bonuses

### Clean Output
- Shows all attempts so you can verify
- Highlights the best guess
- No fancy algorithms, just straightforward logic

## Code Style

- **Simple and readable**: No fancy abstractions or over-engineering
- **Standard Go layout**: Uses `cmd/` and `internal/` packages properly
- **Short method names**: `Decode()`, `Score()`, `TryAllShifts()` - no verbose names
- **Tested**: Unit tests cover the main functionality
- **Interactive**: Accepts input from terminal, not hardcoded

## Requirements

- Go 1.21 or later
- No external dependencies (uses only standard library)

## Project Structure

```
caesarcypher/
├── cmd/
│   └── caesercypher/
│       └── caesercypher.go          # Main executable
├── internal/
│   ├── handlers/
│   │   ├── decoder.go               # Core Caesar cipher logic
│   │   ├── decoder_test.go          # Decoder tests
│   │   └── display.go               # Output formatting
│   └── models/
│       ├── cipher.go                # Data structures
│       └── cipher_test.go           # Model tests
├── go.mod                           # Go module definition
└── README.md                        # This documentation
```

### Package Overview

- **`cmd/caesercypher`**: Main executable entry point
- **`internal/models`**: Core data structures (DecryptionResult, DecryptionAnalysis)
- **`internal/handlers`**: Business logic (CaesarDecoder, DisplayHandler)
  - `decoder.go`: Caesar cipher decryption and scoring algorithms
  - `display.go`: Output formatting and presentation logic

## Author
Onesmus Maenzanise
Created for the Octoco AI Software Engineer coding challenge.