package handlers

import (
	"strings"
	"unicode"

	"caesarcypher/internal/models"
)

type Decoder struct {
	words map[string]int
}

func NewDecoder() *Decoder {
	words := map[string]int{
		"the": 15, "and": 12, "that": 10, "have": 8,
		"for": 8, "not": 8, "with": 8, "you": 8,
		"this": 7, "but": 7, "his": 7, "from": 7,
		"they": 6, "she": 6, "her": 6, "been": 6,
		"than": 6, "its": 6, "now": 6, "more": 6,
		"will": 5, "are": 5, "was": 5, "one": 5,
		"all": 5, "would": 5, "there": 5, "their": 5,
		"we": 4, "can": 4, "had": 4, "what": 4,
		"were": 4, "said": 4, "each": 4, "which": 4,
		"do": 3, "how": 3, "if": 3, "up": 3,
		"out": 3, "many": 3, "then": 3, "them": 3,
		"so": 3, "some": 3, "make": 3, "like": 3,
		"into": 3, "him": 3, "time": 3, "has": 3,
		"two": 3, "go": 2, "no": 2, "way": 2,
		"could": 2, "my": 2, "first": 2, "water": 2,
		"call": 2, "who": 2, "oil": 2, "sit": 2,
		"find": 2, "long": 2, "down": 2, "day": 2,
		"did": 2, "get": 2, "come": 2, "made": 2,
		"may": 2, "part": 2,
	}

	return &Decoder{words: words}
}

func (d *Decoder) Decode(text string, shift int) string {
	result := ""

	for _, char := range text {
		if unicode.IsLetter(char) {
			var base rune
			if unicode.IsUpper(char) {
				base = 'A'
			} else {
				base = 'a'
			}

			shifted := (char - base - rune(shift) + 26) % 26
			result += string(base + shifted)
		} else {
			result += string(char)
		}
	}

	return result
}

func (d *Decoder) Score(text string) int {
	points := 0
	lower := strings.ToLower(text)

	words := strings.Fields(lower)
	for _, word := range words {
		clean := strings.Trim(word, ".,!?;:")
		if val, ok := d.words[clean]; ok {
			points += val
		}
	}

	for _, c := range lower {
		switch c {
		case 'e':
			points += 2
		case 't', 'a', 'o', 'i', 'n':
			points += 1
		}
	}

	return points
}

func (d *Decoder) TryAllShifts(text string) *models.DecryptionAnalysis {
	analysis := models.NewDecryptionAnalysis(text)

	for shift := 1; shift <= 25; shift++ {
		decoded := d.Decode(text, shift)
		points := d.Score(decoded)

		result := models.DecryptionResult{
			Shift:       shift,
			DecodedText: decoded,
			Score:       points,
		}

		analysis.AddResult(result)
	}

	return analysis
}

func (d *Decoder) FindBest(text string) models.DecryptionResult {
	analysis := d.TryAllShifts(text)
	return analysis.BestResult
}
