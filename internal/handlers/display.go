// Package handlers contains display and formatting logic
package handlers

import (
	"fmt"
	"strings"

	"caesarcypher/internal/models"
)

// DisplayHandler handles formatting and display of decryption results
type DisplayHandler struct{}

// NewDisplayHandler creates a new display handler
func NewDisplayHandler() *DisplayHandler {
	return &DisplayHandler{}
}

// ShowHeader displays the application header
func (dh *DisplayHandler) ShowHeader() {
	fmt.Println("Caesar Cipher Decoder")
	fmt.Println("=====================")
	fmt.Println()
}

// ShowAllResults displays all decryption attempts with their scores
func (dh *DisplayHandler) ShowAllResults(analysis *models.DecryptionAnalysis) {
	fmt.Println("Trying all possible shifts:")
	fmt.Println()

	for _, result := range analysis.Results {
		// Show first line of each attempt for transparency
		firstLine := strings.Split(result.DecodedText, "\n")[0]
		if len(firstLine) > 60 {
			firstLine = firstLine[:60] + "..."
		}
		fmt.Printf("Shift %2d (score: %3d): %s\n", result.Shift, result.Score, firstLine)
	}
}

// ShowBestResult displays the highest-scoring decryption result
func (dh *DisplayHandler) ShowBestResult(bestResult models.DecryptionResult) {
	fmt.Println()
	fmt.Println("=" + strings.Repeat("=", 60))
	fmt.Printf("Best guess (shift %d, score: %d):\n", bestResult.Shift, bestResult.Score)
	fmt.Println("=" + strings.Repeat("=", 60))
	fmt.Println()
	fmt.Println(bestResult.DecodedText)
	fmt.Println()
}

// ShowCompleteAnalysis displays both all results and the best result
func (dh *DisplayHandler) ShowCompleteAnalysis(analysis *models.DecryptionAnalysis) {
	dh.ShowHeader()
	dh.ShowAllResults(analysis)
	dh.ShowBestResult(analysis.BestResult)
}

// ShowSummary displays a brief summary of the decryption
func (dh *DisplayHandler) ShowSummary(analysis *models.DecryptionAnalysis) {
	fmt.Printf("Decrypted with shift %d (score: %d)\n",
		analysis.BestResult.Shift, analysis.BestResult.Score)
	fmt.Println(analysis.BestResult.DecodedText)
}
