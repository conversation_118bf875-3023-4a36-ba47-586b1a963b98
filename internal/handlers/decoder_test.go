package handlers

import (
	"testing"
)

func TestDecodeCaesar(t *testing.T) {
	decoder := NewDecoder()

	tests := []struct {
		name     string
		input    string
		shift    int
		expected string
	}{
		{
			name:     "Simple lowercase",
			input:    "abc",
			shift:    1,
			expected: "zab",
		},
		{
			name:     "Simple uppercase",
			input:    "ABC",
			shift:    1,
			expected: "ZAB",
		},
		{
			name:     "Mixed case with punctuation",
			input:    "Hello, World!",
			shift:    13,
			expected: "Uryyb, Jbeyq!",
		},
		{
			name:     "Wrap around",
			input:    "xyz",
			shift:    3,
			expected: "uvw",
		},
		{
			name:     "Numbers and symbols unchanged",
			input:    "Test123!@#",
			shift:    5,
			expected: "Ozno123!@#",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := decoder.Decode(tt.input, tt.shift)
			if result != tt.expected {
				t.<PERSON>rrorf("decode(%q, %d) = %q, want %q", tt.input, tt.shift, result, tt.expected)
			}
		})
	}
}

func TestScoreText(t *testing.T) {
	decoder := NewDecoder()

	tests := []struct {
		name     string
		input    string
		minScore int
	}{
		{
			name:     "Common English text",
			input:    "The quick brown fox jumps over the lazy dog",
			minScore: 20,
		},
		{
			name:     "Gibberish",
			input:    "xyz qwerty asdfgh",
			minScore: 0,
		},
		{
			name:     "Empty string",
			input:    "",
			minScore: 0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			score := decoder.Score(tt.input)
			if score < tt.minScore {
				t.Errorf("score(%q) = %d, want at least %d", tt.input, score, tt.minScore)
			}
		})
	}
}

func TestAnalyzeAllShifts(t *testing.T) {
	decoder := NewDecoder()
	ciphertext := "Wkh txlfn eurzq ira"

	analysis := decoder.TryAllShifts(ciphertext)

	if len(analysis.Results) != 25 {
		t.Errorf("Expected 25 results, got %d", len(analysis.Results))
	}

	if analysis.OriginalText != ciphertext {
		t.Errorf("Original text not stored correctly")
	}

	if analysis.BestResult.Shift < 1 || analysis.BestResult.Shift > 25 {
		t.Errorf("Best result shift %d is out of valid range", analysis.BestResult.Shift)
	}

	if analysis.BestResult.Score <= 0 {
		t.Errorf("Best result should have a positive score, got %d", analysis.BestResult.Score)
	}
}

func TestFindBestDecryption(t *testing.T) {
	decoder := NewDecoder()

	ciphertext := "Wkh txlfn eurzq ira mxpsv ryhu wkh odcb grj"

	result := decoder.FindBest(ciphertext)

	if result.Score <= 0 {
		t.Errorf("findBest should find a positive score, got %d", result.Score)
	}

	if result.Shift != 3 {
		t.Logf("Expected shift 3, got shift %d with text: %q", result.Shift, result.DecodedText)
	}
}
