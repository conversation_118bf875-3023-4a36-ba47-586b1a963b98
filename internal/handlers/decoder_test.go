package handlers

import (
	"testing"
)

func TestDecodeCaesar(t *testing.T) {
	decoder := NewCaesarDecoder()

	tests := []struct {
		name     string
		input    string
		shift    int
		expected string
	}{
		{
			name:     "Simple lowercase",
			input:    "abc",
			shift:    1,
			expected: "zab",
		},
		{
			name:     "Simple uppercase",
			input:    "ABC",
			shift:    1,
			expected: "ZAB",
		},
		{
			name:     "Mixed case with punctuation",
			input:    "Hello, World!",
			shift:    13,
			expected: "Uryyb, Jbeyq!",
		},
		{
			name:     "Wrap around",
			input:    "xyz",
			shift:    3,
			expected: "uvw",
		},
		{
			name:     "Numbers and symbols unchanged",
			input:    "Test123!@#",
			shift:    5,
			expected: "Ozno123!@#",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := decoder.DecodeCaesar(tt.input, tt.shift)
			if result != tt.expected {
				t.<PERSON>("DecodeCaesar(%q, %d) = %q, want %q", tt.input, tt.shift, result, tt.expected)
			}
		})
	}
}

func TestScoreText(t *testing.T) {
	decoder := NewCaesarDecoder()

	tests := []struct {
		name     string
		input    string
		minScore int // We expect at least this score
	}{
		{
			name:     "Common English text",
			input:    "The quick brown fox jumps over the lazy dog",
			minScore: 20, // Should score well due to "the" appearing twice
		},
		{
			name:     "Gibberish",
			input:    "xyz qwerty asdfgh",
			minScore: 0, // Should score low
		},
		{
			name:     "Empty string",
			input:    "",
			minScore: 0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			score := decoder.ScoreText(tt.input)
			if score < tt.minScore {
				t.Errorf("ScoreText(%q) = %d, want at least %d", tt.input, score, tt.minScore)
			}
		})
	}
}

func TestAnalyzeAllShifts(t *testing.T) {
	decoder := NewCaesarDecoder()
	ciphertext := "Wkh txlfn eurzq ira" // "The quick brown fox" with shift 3

	analysis := decoder.AnalyzeAllShifts(ciphertext)

	// Should have 25 results (shifts 1-25)
	if len(analysis.Results) != 25 {
		t.Errorf("Expected 25 results, got %d", len(analysis.Results))
	}

	// Should have the original text stored
	if analysis.OriginalText != ciphertext {
		t.Errorf("Original text not stored correctly")
	}

	// Best result should be shift 3 (or close to it)
	if analysis.BestResult.Shift < 1 || analysis.BestResult.Shift > 25 {
		t.Errorf("Best result shift %d is out of valid range", analysis.BestResult.Shift)
	}

	// Best result should have a reasonable score
	if analysis.BestResult.Score <= 0 {
		t.Errorf("Best result should have a positive score, got %d", analysis.BestResult.Score)
	}
}

func TestFindBestDecryption(t *testing.T) {
	decoder := NewCaesarDecoder()

	// Test with a known Caesar cipher - use a more recognizable English phrase
	ciphertext := "Wkh txlfn eurzq ira mxpsv ryhu wkh odcb grj" // "The quick brown fox jumps over the lazy dog" with shift 3

	// Now find the best decryption
	result := decoder.FindBestDecryption(ciphertext)

	// The result should be readable English
	if result.Score <= 0 {
		t.Errorf("FindBestDecryption should find a positive score, got %d", result.Score)
	}

	// Should find shift 3 as the best
	if result.Shift != 3 {
		t.Logf("Expected shift 3, got shift %d with text: %q", result.Shift, result.DecodedText)
		// Don't fail the test as scoring might vary, just log it
	}
}
