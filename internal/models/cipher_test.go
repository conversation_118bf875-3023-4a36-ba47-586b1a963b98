package models

import (
	"testing"
)

func TestNewDecryptionAnalysis(t *testing.T) {
	originalText := "test cipher text"
	analysis := NewDecryptionAnalysis(originalText)

	if analysis.OriginalText != originalText {
		t.<PERSON><PERSON><PERSON>("Expected original text %q, got %q", originalText, analysis.OriginalText)
	}

	if len(analysis.Results) != 0 {
		t.<PERSON><PERSON>("Expected empty results slice, got %d results", len(analysis.Results))
	}

	if analysis.BestResult.Score != 0 {
		t.<PERSON><PERSON><PERSON>("Expected best result score to be 0, got %d", analysis.BestResult.Score)
	}
}

func TestAddResult(t *testing.T) {
	analysis := NewDecryptionAnalysis("test")

	// Add first result
	result1 := DecryptionResult{
		Shift:       1,
		DecodedText: "decoded1",
		Score:       10,
	}
	analysis.AddResult(result1)

	if len(analysis.Results) != 1 {
		t.<PERSON><PERSON><PERSON>("Expected 1 result, got %d", len(analysis.Results))
	}

	if analysis.BestResult.Score != 10 {
		t.<PERSON><PERSON><PERSON>("Expected best score 10, got %d", analysis.BestResult.Score)
	}

	// Add better result
	result2 := DecryptionResult{
		Shift:       2,
		DecodedText: "decoded2",
		Score:       20,
	}
	analysis.AddResult(result2)

	if len(analysis.Results) != 2 {
		t.Errorf("Expected 2 results, got %d", len(analysis.Results))
	}

	if analysis.BestResult.Score != 20 {
		t.Errorf("Expected best score 20, got %d", analysis.BestResult.Score)
	}

	if analysis.BestResult.Shift != 2 {
		t.Errorf("Expected best shift 2, got %d", analysis.BestResult.Shift)
	}

	// Add worse result - should not change best
	result3 := DecryptionResult{
		Shift:       3,
		DecodedText: "decoded3",
		Score:       5,
	}
	analysis.AddResult(result3)

	if analysis.BestResult.Score != 20 {
		t.Errorf("Best result should still be 20, got %d", analysis.BestResult.Score)
	}
}

func TestGetResultByShift(t *testing.T) {
	analysis := NewDecryptionAnalysis("test")

	result1 := DecryptionResult{
		Shift:       5,
		DecodedText: "decoded5",
		Score:       15,
	}
	result2 := DecryptionResult{
		Shift:       10,
		DecodedText: "decoded10",
		Score:       25,
	}

	analysis.AddResult(result1)
	analysis.AddResult(result2)

	// Test finding existing result
	found := analysis.GetResultByShift(5)
	if found == nil {
		t.Error("Expected to find result for shift 5")
	} else {
		if found.DecodedText != "decoded5" {
			t.Errorf("Expected decoded text 'decoded5', got %q", found.DecodedText)
		}
		if found.Score != 15 {
			t.Errorf("Expected score 15, got %d", found.Score)
		}
	}

	// Test finding non-existing result
	notFound := analysis.GetResultByShift(99)
	if notFound != nil {
		t.Error("Expected nil for non-existing shift")
	}
}

func TestDecryptionResult(t *testing.T) {
	result := DecryptionResult{
		Shift:       7,
		DecodedText: "Hello World",
		Score:       42,
	}

	if result.Shift != 7 {
		t.Errorf("Expected shift 7, got %d", result.Shift)
	}

	if result.DecodedText != "Hello World" {
		t.Errorf("Expected decoded text 'Hello World', got %q", result.DecodedText)
	}

	if result.Score != 42 {
		t.Errorf("Expected score 42, got %d", result.Score)
	}
}
