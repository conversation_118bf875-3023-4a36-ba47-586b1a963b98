package main

import (
	"strings"
	"testing"
)

func TestDecodeCaesar(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		shift    int
		expected string
	}{
		{
			name:     "Simple lowercase",
			input:    "abc",
			shift:    1,
			expected: "zab",
		},
		{
			name:     "Simple uppercase",
			input:    "ABC",
			shift:    1,
			expected: "ZAB",
		},
		{
			name:     "Mixed case with punctuation",
			input:    "Hello, World!",
			shift:    13,
			expected: "<PERSON>ryyb, Jbeyq!",
		},
		{
			name:     "Wrap around",
			input:    "xyz",
			shift:    3,
			expected: "uvw",
		},
		{
			name:     "Numbers and symbols unchanged",
			input:    "Test123!@#",
			shift:    5,
			expected: "Ozno123!@#",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := decodeCaesar(tt.input, tt.shift)
			if result != tt.expected {
				t.<PERSON>rrorf("decodeCaesar(%q, %d) = %q, want %q", tt.input, tt.shift, result, tt.expected)
			}
		})
	}
}

func TestScoreText(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		minScore int // We expect at least this score
	}{
		{
			name:     "Common English text",
			input:    "The quick brown fox jumps over the lazy dog",
			minScore: 20, // Should score well due to "the" appearing twice
		},
		{
			name:     "Gibberish",
			input:    "xyz qwerty asdfgh",
			minScore: 0, // Should score low
		},
		{
			name:     "Empty string",
			input:    "",
			minScore: 0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			score := scoreText(tt.input)
			if score < tt.minScore {
				t.Errorf("scoreText(%q) = %d, want at least %d", tt.input, score, tt.minScore)
			}
		})
	}
}

func TestFullDecryption(t *testing.T) {
	// Test with a known Caesar cipher
	plaintext := "HELLO WORLD"
	shift := 3
	
	// Encrypt it (reverse of decode)
	encrypted := encodeCaesar(plaintext, shift)
	
	// Now decode it
	decoded := decodeCaesar(encrypted, shift)
	
	if decoded != plaintext {
		t.Errorf("Full encryption/decryption failed: got %q, want %q", decoded, plaintext)
	}
}

// Helper function for testing - encodes with Caesar cipher
func encodeCaesar(text string, shift int) string {
	var result strings.Builder
	
	for _, char := range text {
		if char >= 'A' && char <= 'Z' {
			shifted := (char - 'A' + rune(shift)) % 26
			result.WriteRune('A' + shifted)
		} else if char >= 'a' && char <= 'z' {
			shifted := (char - 'a' + rune(shift)) % 26
			result.WriteRune('a' + shifted)
		} else {
			result.WriteRune(char)
		}
	}
	
	return result.String()
}
