// Caesar Cipher Decoder
// Simple brute-force decoder for Caesar-shifted text.
// Written for clarity, not golfed for brevity.

package main

import (
	"fmt"
	"strings"
	"unicode"
)

// decodeCaesar shifts letters backward by the given shift amount
func decodeCaesar(text string, shift int) string {
	var result strings.Builder

	for _, char := range text {
		if unicode.IsLetter(char) {
			// Determine if uppercase or lowercase
			var base rune
			if unicode.IsUpper(char) {
				base = 'A'
			} else {
				base = 'a'
			}

			// Shift the character backward
			shifted := (char - base - rune(shift) + 26) % 26
			result.WriteRune(base + shifted)
		} else {
			// Keep non-letters unchanged
			result.WriteRune(char)
		}
	}

	return result.String()
}

// scoreText assigns a score based on common English words and patterns
func scoreText(text string) int {
	score := 0
	lowerText := strings.ToLower(text)

	// Common English words - higher frequency words get higher scores
	commonWords := map[string]int{
		"the": 15, "and": 12, "that": 10, "have": 8,
		"for": 8, "not": 8, "with": 8, "you": 8,
		"this": 7, "but": 7, "his": 7, "from": 7,
		"they": 6, "she": 6, "her": 6, "been": 6,
		"than": 6, "its": 6, "now": 6, "more": 6,
		"will": 5, "are": 5, "was": 5, "one": 5,
		"all": 5, "would": 5, "there": 5, "their": 5,
		"we": 4, "can": 4, "had": 4, "what": 4,
		"were": 4, "said": 4, "each": 4, "which": 4,
		"do": 3, "how": 3, "if": 3, "up": 3,
		"out": 3, "many": 3, "then": 3, "them": 3,
		"so": 3, "some": 3, "make": 3, "like": 3,
		"into": 3, "him": 3, "time": 3, "has": 3,
		"two": 3, "go": 2, "no": 2, "way": 2,
		"could": 2, "my": 2, "first": 2, "water": 2,
		"call": 2, "who": 2, "oil": 2, "sit": 2,
		"find": 2, "long": 2, "down": 2, "day": 2,
		"did": 2, "get": 2, "come": 2, "made": 2,
		"may": 2, "part": 2,
	}

	// Split text into words and check against common words
	words := strings.Fields(lowerText)
	for _, word := range words {
		// Remove punctuation from word
		cleanWord := strings.Trim(word, ".,!?;:")
		if wordScore, exists := commonWords[cleanWord]; exists {
			score += wordScore
		}
	}

	// Bonus points for reasonable letter frequency
	// English has high frequency of 'e', 't', 'a', 'o', 'i', 'n'
	for _, char := range lowerText {
		switch char {
		case 'e':
			score += 2
		case 't', 'a', 'o', 'i', 'n':
			score += 1
		}
	}

	return score
}

func main() {
	// The encrypted message from the challenge
	ciphertext := `Nzyrclefwletzyd zy nclnvtyr esp Zneznz LT
nslwwpyrp - txacpddtgp aczmwpx-dzwgtyr!
Hp lcp ly LT Tyyzgletzy Defotz esle spwad
eplxd efcyc mzwo LT topld tyez glwflmwp,
fdlmwp, lyo dnlwlmwp aczofned. Dpyo jzfcc
NG lyo dzwfetzy <NAME_EMAIL>.
Wpe'd mftwo dzxpestyr pieclzcotylcj
ezrpespc!`

	fmt.Println("Caesar Cipher Decoder")
	fmt.Println("=====================")
	fmt.Println()

	bestScore := 0
	bestShift := 0
	bestDecoded := ""

	fmt.Println("Trying all possible shifts:")
	fmt.Println()

	// Try all possible shifts (1-25)
	for shift := 1; shift <= 25; shift++ {
		decoded := decodeCaesar(ciphertext, shift)
		score := scoreText(decoded)

		// Show first line of each attempt for transparency
		firstLine := strings.Split(decoded, "\n")[0]
		if len(firstLine) > 60 {
			firstLine = firstLine[:60] + "..."
		}
		fmt.Printf("Shift %2d (score: %3d): %s\n", shift, score, firstLine)

		// Track the best result
		if score > bestScore {
			bestScore = score
			bestShift = shift
			bestDecoded = decoded
		}
	}

	fmt.Println()
	fmt.Println("=" + strings.Repeat("=", 60))
	fmt.Printf("Best guess (shift %d, score: %d):\n", bestShift, bestScore)
	fmt.Println("=" + strings.Repeat("=", 60))
	fmt.Println()
	fmt.Println(bestDecoded)
	fmt.Println()

	// TODO: Could add more sophisticated scoring based on bigram/trigram frequency
	// TODO: Could also detect language automatically for polyglot support
}
