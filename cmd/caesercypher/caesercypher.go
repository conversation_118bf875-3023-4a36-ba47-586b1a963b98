package main

import (
	"bufio"
	"fmt"
	"os"
	"strings"

	"caesarcypher/internal/handlers"
)

func main() {
	fmt.Println("Caesar Cipher Decoder")
	fmt.Println("Enter the encrypted text (press Enter twice when done):")

	scanner := bufio.NewScanner(os.Stdin)
	var lines []string

	for scanner.Scan() {
		line := scanner.Text()
		if line == "" {
			break
		}
		lines = append(lines, line)
	}

	if len(lines) == 0 {
		fmt.Println("No input provided")
		return
	}

	ciphertext := strings.Join(lines, "\n")

	decoder := handlers.NewDecoder()
	display := handlers.NewDisplayHandler()

	analysis := decoder.TryAllShifts(ciphertext)
	display.ShowCompleteAnalysis(analysis)
}
