# Caesar Cipher Decoder Makefile

.PHONY: run test build clean help

# Default target
help:
	@echo "Available targets:"
	@echo "  run    - Run the Caesar cipher decoder (interactive)"
	@echo "  demo   - Run with the challenge text"
	@echo "  test   - Run all tests"
	@echo "  build  - Build the executable"
	@echo "  clean  - Clean build artifacts"
	@echo "  help   - Show this help message"

# Run the decoder
run:
	go run cmd/caesercypher/caesercypher.go

# Run tests
test:
	go test ./...

# Run tests with verbose output
test-verbose:
	go test -v ./...

# Build the executable
build:
	go build -o bin/caesercypher cmd/caesercypher/caesercypher.go

# Clean build artifacts
clean:
	rm -rf bin/

# Format code
fmt:
	go fmt ./...

# Run linter (requires golangci-lint)
lint:
	golangci-lint run

# Run tests with coverage
test-coverage:
	go test -cover ./...

# Demo with the challenge text
demo:
	@printf "Nzyrclefwletzyd zy nclnvtyr esp Zneznz LT\nnslwwpyrp - txacpddtgp aczmwpx-dzwgtyr!\nHp lcp ly LT Tyyzgletzy Defotz esle spwad\neplxd efcyc mzwo LT topld tyez glwflmwp,\nfdlmwp, lyo dnlwlmwp aczofned. Dpyo jzfcc\nNG lyo dzwfetzy <NAME_EMAIL>.\nWpe'd mftwo dzxpestyr pieclzcotylcj\nezrpespc!\n\n" | go run cmd/caesercypher/caesercypher.go
